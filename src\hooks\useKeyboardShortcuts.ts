import { useEffect, useCallback } from 'react';
import { useHotkeys } from './useHotkeys';
import type { KeyboardShortcut } from '../types';

interface UseKeyboardShortcutsProps {
  onNewConversation?: () => void;
  onSettings?: () => void;
  onToggleExecutionMode?: () => void;
  onClearMessages?: () => void;
  onFocusInput?: () => void;
  onCopyLastResponse?: () => void;
  onToggleTheme?: () => void;
  onShowHelp?: () => void;
  enabled?: boolean;
}

export const useKeyboardShortcuts = ({
  onNewConversation,
  onSettings,
  onToggleExecutionMode,
  onClearMessages,
  onFocusInput,
  onCopyLastResponse,
  onToggleTheme,
  onShowHelp,
  enabled = true
}: UseKeyboardShortcutsProps = {}) => {
  
  // Define keyboard shortcuts
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'ctrl+n, cmd+n',
      description: 'New conversation',
      action: () => onNewConversation?.()
    },
    {
      key: 'ctrl+comma, cmd+comma',
      description: 'Open settings',
      action: () => onSettings?.()
    },
    {
      key: 'ctrl+shift+y, cmd+shift+y',
      description: 'Toggle YOLO mode',
      action: () => onToggleExecutionMode?.()
    },
    {
      key: 'ctrl+shift+c, cmd+shift+c',
      description: 'Clear conversation',
      action: () => onClearMessages?.()
    },
    {
      key: 'ctrl+l, cmd+l',
      description: 'Focus input',
      action: () => onFocusInput?.()
    },
    {
      key: 'ctrl+shift+l, cmd+shift+l',
      description: 'Copy last response',
      action: () => onCopyLastResponse?.()
    },
    {
      key: 'ctrl+shift+t, cmd+shift+t',
      description: 'Toggle theme',
      action: () => onToggleTheme?.()
    },
    {
      key: 'ctrl+shift+slash, cmd+shift+slash',
      description: 'Show keyboard shortcuts',
      action: () => onShowHelp?.()
    }
  ];

  // Register hotkeys
  useHotkeys(['ctrl+n', 'cmd+n'], () => enabled && onNewConversation?.(), {
    enableOnFormTags: false
  });

  useHotkeys(['ctrl+comma', 'cmd+comma'], () => enabled && onSettings?.(), {
    enableOnFormTags: false
  });

  useHotkeys(['ctrl+shift+y', 'cmd+shift+y'], () => enabled && onToggleExecutionMode?.(), {
    enableOnFormTags: false
  });

  useHotkeys(['ctrl+shift+c', 'cmd+shift+c'], () => enabled && onClearMessages?.(), {
    enableOnFormTags: false
  });

  useHotkeys(['ctrl+l', 'cmd+l'], () => enabled && onFocusInput?.(), {
    enableOnFormTags: false
  });

  useHotkeys(['ctrl+shift+l', 'cmd+shift+l'], () => enabled && onCopyLastResponse?.(), {
    enableOnFormTags: false
  });

  useHotkeys(['ctrl+shift+t', 'cmd+shift+t'], () => enabled && onToggleTheme?.(), {
    enableOnFormTags: false
  });

  useHotkeys(['ctrl+shift+slash', 'cmd+shift+slash'], () => enabled && onShowHelp?.(), {
    enableOnFormTags: false
  });

  // Global escape handler
  useHotkeys('escape', () => {
    // Close any open modals or cancel operations
    const event = new CustomEvent('global-escape');
    window.dispatchEvent(event);
  }, {
    enableOnFormTags: true
  });

  return {
    shortcuts,
    enabled
  };
};

// Hook for showing keyboard shortcuts help
export const useKeyboardShortcutsHelp = () => {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'Ctrl+N / Cmd+N',
      description: 'Start a new conversation',
      action: () => {}
    },
    {
      key: 'Ctrl+, / Cmd+,',
      description: 'Open settings',
      action: () => {}
    },
    {
      key: 'Ctrl+Shift+Y / Cmd+Shift+Y',
      description: 'Toggle YOLO mode (execute without confirmation)',
      action: () => {}
    },
    {
      key: 'Ctrl+Shift+C / Cmd+Shift+C',
      description: 'Clear current conversation',
      action: () => {}
    },
    {
      key: 'Ctrl+L / Cmd+L',
      description: 'Focus on input field',
      action: () => {}
    },
    {
      key: 'Ctrl+Shift+L / Cmd+Shift+L',
      description: 'Copy last AI response to clipboard',
      action: () => {}
    },
    {
      key: 'Ctrl+Shift+T / Cmd+Shift+T',
      description: 'Toggle dark/light theme',
      action: () => {}
    },
    {
      key: 'Ctrl+Shift+? / Cmd+Shift+?',
      description: 'Show this help dialog',
      action: () => {}
    },
    {
      key: 'Enter',
      description: 'Send message (in input field)',
      action: () => {}
    },
    {
      key: 'Shift+Enter',
      description: 'New line (in input field)',
      action: () => {}
    },
    {
      key: '↑ / ↓',
      description: 'Navigate through message history (in input field)',
      action: () => {}
    },
    {
      key: 'Escape',
      description: 'Close modals or cancel operations',
      action: () => {}
    }
  ];

  return shortcuts;
};

// Hook for handling global keyboard events
export const useGlobalKeyboardEvents = () => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Handle global keyboard events that don't need specific handlers
    
    // Prevent default browser shortcuts that might interfere
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'r': // Prevent refresh
          if (event.shiftKey) {
            event.preventDefault();
          }
          break;
        case 'w': // Prevent close tab
          event.preventDefault();
          break;
        case 't': // Prevent new tab (unless it's our theme toggle)
          if (!event.shiftKey) {
            event.preventDefault();
          }
          break;
      }
    }

    // Handle F-keys
    switch (event.key) {
      case 'F5': // Prevent refresh
        event.preventDefault();
        break;
      case 'F11': // Allow fullscreen
        break;
      case 'F12': // Allow dev tools
        break;
      default:
        if (event.key.startsWith('F')) {
          event.preventDefault();
        }
    }
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};

// Hook for copying text to clipboard
export const useClipboard = () => {
  const copyToClipboard = useCallback(async (text: string): Promise<boolean> => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const success = document.execCommand('copy');
        document.body.removeChild(textArea);
        return success;
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      return false;
    }
  }, []);

  const readFromClipboard = useCallback(async (): Promise<string | null> => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        return await navigator.clipboard.readText();
      }
      return null;
    } catch (error) {
      console.error('Failed to read from clipboard:', error);
      return null;
    }
  }, []);

  return {
    copyToClipboard,
    readFromClipboard
  };
};
