import React, { useRef, useEffect, useCallback } from 'react';
import { useAppStore } from '../store';

interface PromptInputProps {
  onSubmit: (prompt: string) => void;
  disabled?: boolean;
}

export const PromptInput: React.FC<PromptInputProps> = ({ onSubmit, disabled = false }) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const {
    currentPrompt,
    setCurrentPrompt,
    addToPromptHistory,
    navigateHistory,
  } = useAppStore();

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';
    
    // Calculate new height (min 40px, max 200px)
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 200);
    textarea.style.height = `${newHeight}px`;
  }, []);

  // Adjust height when content changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [currentPrompt, adjustTextareaHeight]);

  // Focus textarea on mount
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  const handleSubmit = useCallback(() => {
    const trimmedPrompt = currentPrompt.trim();
    if (trimmedPrompt === '') return;

    // Add to history and submit
    addToPromptHistory(trimmedPrompt);
    onSubmit(trimmedPrompt);
    
    // Clear current prompt
    setCurrentPrompt('');
  }, [currentPrompt, addToPromptHistory, onSubmit, setCurrentPrompt]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: new line (default behavior)
        return;
      } else {
        // Enter: submit
        e.preventDefault();
        handleSubmit();
      }
    } else if (e.key === 'ArrowUp' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
      // Navigate to previous prompt in history
      const textarea = textareaRef.current;
      if (textarea && textarea.selectionStart === 0 && textarea.selectionEnd === 0) {
        e.preventDefault();
        const historicalPrompt = navigateHistory('up');
        if (historicalPrompt !== '') {
          setCurrentPrompt(historicalPrompt);
        }
      }
    } else if (e.key === 'ArrowDown' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
      // Navigate to next prompt in history
      const textarea = textareaRef.current;
      if (textarea) {
        const isAtEnd = textarea.selectionStart === textarea.value.length && 
                       textarea.selectionEnd === textarea.value.length;
        if (isAtEnd) {
          e.preventDefault();
          const historicalPrompt = navigateHistory('down');
          setCurrentPrompt(historicalPrompt);
        }
      }
    }
  }, [handleSubmit, navigateHistory, setCurrentPrompt]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCurrentPrompt(e.target.value);
  }, [setCurrentPrompt]);

  return (
    <div className="input-container p-4">
      <div className="w-full">
        <div className="flex gap-4 items-end">
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={currentPrompt}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              disabled={disabled}
              className="input-textarea w-full px-4 py-3 pr-16 disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                minHeight: '52px',
                maxHeight: '200px',
                lineHeight: '1.5',
              }}
              rows={1}
            />

            {/* Character count indicator for long messages */}
            {currentPrompt.length > 500 && (
              <div className="absolute bottom-3 right-16 text-xs text-[#71717a] bg-[#1e1e1e] px-2 py-1 rounded-md shadow-sm">
                {currentPrompt.length}
              </div>
            )}

            {/* Send button inside textarea */}
            <button
              onClick={handleSubmit}
              disabled={disabled || currentPrompt.trim() === ''}
              className="absolute bottom-2 right-2 w-8 h-8 bg-[#2563eb] hover:bg-[#1d4ed8] disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-md transition-all duration-200 flex items-center justify-center shadow-sm"
              title="Send message (Enter)"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </div>

        {/* Keyboard shortcuts help */}
        <div className="mt-4 flex gap-6 text-xs text-[#71717a] justify-center">
          <div className="flex items-center gap-1">
            <kbd className="px-2 py-1 bg-[#1e1e1e] border border-[#27272a] rounded text-xs font-mono">Enter</kbd>
            <span>to send</span>
          </div>
          <div className="flex items-center gap-1">
            <kbd className="px-2 py-1 bg-[#1e1e1e] border border-[#27272a] rounded text-xs font-mono">Shift+Enter</kbd>
            <span>for new line</span>
          </div>
          <div className="flex items-center gap-1">
            <kbd className="px-2 py-1 bg-[#1e1e1e] border border-[#27272a] rounded text-xs font-mono">↑↓</kbd>
            <span>to navigate history</span>
          </div>
        </div>
      </div>
    </div>
  );
};
