import React from 'react';
import type { AgentState, AppConfig } from '../types';

interface FooterStatusBarProps {
  agentState: AgentState;
  config: AppConfig;
  onSettingsClick: () => void;
}

export const FooterStatusBar: React.FC<FooterStatusBarProps> = ({
  agentState,
  config,
  onSettingsClick
}) => {
  const getStatusIcon = () => {
    if (agentState.isProcessing) {
      return (
        <svg className="w-4 h-4 animate-spin text-[#2563eb]" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      );
    }

    if (agentState.awaitingConfirmation) {
      return (
        <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      );
    }

    if (agentState.executingTools) {
      return (
        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      );
    }

    if (agentState.streamingResponse) {
      return (
        <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      );
    }

    return (
      <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    );
  };

  const getStatusText = () => {
    if (agentState.isProcessing) return 'Processing...';
    if (agentState.awaitingConfirmation) return 'Awaiting confirmation';
    if (agentState.executingTools) return 'Executing tools';
    if (agentState.streamingResponse) return 'Generating response';
    return 'Ready';
  };

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'openai':
        return '🤖';
      case 'anthropic':
        return '🧠';
      case 'deepseek':
        return '🔍';
      default:
        return '🤖';
    }
  };

  const isYoloMode = config.executionMode === 'yolo';

  return (
    <div className={`
      flex items-center justify-between px-4 py-2 bg-[#111111] border-t border-[#27272a]
      ${isYoloMode ? 'border-t-2 border-t-red-500' : ''}
    `}>
      {/* Left side - Agent Status */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className="text-sm text-[#a1a1aa]">{getStatusText()}</span>
        </div>

        {/* Current Plan Indicator */}
        {agentState.currentPlan && (
          <div className="flex items-center space-x-2 text-xs text-[#71717a]">
            <span>•</span>
            <span className="truncate max-w-48">{agentState.currentPlan.description}</span>
          </div>
        )}
      </div>

      {/* Center - YOLO Mode Warning */}
      {isYoloMode && (
        <div className="flex items-center space-x-2 px-3 py-1 bg-red-500/10 border border-red-500/20 rounded-full">
          <svg className="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span className="text-sm font-medium text-red-400">YOLO MODE</span>
        </div>
      )}

      {/* Right side - Provider Info & Settings */}
      <div className="flex items-center space-x-4">
        {/* LLM Provider */}
        <div className="flex items-center space-x-2 text-sm text-[#a1a1aa]">
          <span>{getProviderIcon(config.llm.provider)}</span>
          <span className="capitalize">{config.llm.provider}</span>
          <span className="text-[#71717a]">•</span>
          <span className="text-[#71717a]">{config.llm.model}</span>
        </div>

        {/* Execution Mode Badge */}
        <div className={`
          px-2 py-1 rounded-full text-xs font-medium
          ${isYoloMode 
            ? 'bg-red-500/10 text-red-400 border border-red-500/20' 
            : 'bg-green-500/10 text-green-400 border border-green-500/20'
          }
        `}>
          {isYoloMode ? 'YOLO' : 'SAFE'}
        </div>

        {/* Settings Button */}
        <button
          onClick={onSettingsClick}
          className="p-1.5 hover:bg-[#252525] rounded-lg transition-colors"
          title="Settings"
        >
          <svg className="w-4 h-4 text-[#71717a] hover:text-[#a1a1aa]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default FooterStatusBar;
