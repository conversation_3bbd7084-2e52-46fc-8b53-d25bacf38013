import React from 'react';

const App: React.FC = () => {
  return (
    <div className="h-screen bg-red-500 text-white flex items-center justify-center">
      <h1 className="text-4xl">Test App - If you see this, React is working!</h1>
    </div>
  );
};
  const {
    config,
    updateConfig,
    showSettings,
    setShowSettings,
    showConfirmation,
    setShowConfirmation,
    addToPromptHistory,
    setCurrentPrompt,
    setError
  } = useAppStore();

  // Use the agent loop hook for actual message processing
  // Temporarily commented out to debug black screen issue
  /*
  const {
    agentState,
    messages,
    currentPlan,
    error,
    canSendMessage,
    lastAIMessage,
    processPrompt,
    confirmPlan,
    cancelPlan,
    clearMessages: clearAgentMessages
  } = useAgentLoop({ config });
  */

  // Temporary fallback values for debugging
  const agentState = { isProcessing: false, awaitingConfirmation: false, executingTools: false, streamingResponse: false };
  const messages: any[] = [];
  const currentPlan = null;
  const error = null;
  const canSendMessage = true;
  const lastAIMessage = null;
  const processPrompt = async (prompt: string) => console.log('Mock processPrompt:', prompt);
  const confirmPlan = (plan: any) => console.log('Mock confirmPlan:', plan);
  const cancelPlan = () => console.log('Mock cancelPlan');
  const clearAgentMessages = () => console.log('Mock clearAgentMessages');

  const { copyToClipboard } = useClipboard();

  // Set up global keyboard events
  useGlobalKeyboardEvents();

  // Handle keyboard shortcuts
  const handleNewConversation = useCallback(() => {
    clearAgentMessages();
  }, [clearAgentMessages]);

  const handleToggleExecutionMode = useCallback(() => {
    const newMode = config.executionMode === 'confirm' ? 'yolo' : 'confirm';
    updateConfig({ executionMode: newMode });
  }, [config.executionMode, updateConfig]);

  const handleFocusInput = useCallback(() => {
    // Focus the input textarea
    const textarea = document.querySelector('textarea[placeholder="Type your message..."]') as HTMLTextAreaElement;
    if (textarea) {
      textarea.focus();
    }
  }, []);

  const handleCopyLastResponse = useCallback(() => {
    if (lastAIMessage) {
      copyToClipboard(lastAIMessage.content);
    }
  }, [lastAIMessage, copyToClipboard]);

  const handlePromptSubmit = useCallback(async (prompt: string) => {
    try {
      await processPrompt(prompt);
    } catch (error) {
      console.error('Error processing prompt:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    }
  }, [processPrompt, setError]);

  useKeyboardShortcuts({
    onNewConversation: handleNewConversation,
    onSettings: () => setShowSettings(true),
    onToggleExecutionMode: handleToggleExecutionMode,
    onClearMessages: handleNewConversation,
    onFocusInput: handleFocusInput,
    onCopyLastResponse: handleCopyLastResponse,
    enabled: !showSettings && !showConfirmation
  });

  const handleConfirmPlan = useCallback(() => {
    if (currentPlan) {
      confirmPlan(currentPlan);
      setShowConfirmation(false);
    }
  }, [currentPlan, confirmPlan, setShowConfirmation]);

  const handleCancelPlan = useCallback(() => {
    if (currentPlan) {
      cancelPlan();
      setShowConfirmation(false);
    }
  }, [currentPlan, cancelPlan, setShowConfirmation]);

  // Show confirmation modal when plan requires confirmation
  useEffect(() => {
    if (currentPlan && currentPlan.requiresConfirmation && config.executionMode === 'confirm') {
      setShowConfirmation(true);
    }
  }, [currentPlan, config.executionMode, setShowConfirmation]);

  // Auto-focus input on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      handleFocusInput();
    }, 100);
    return () => clearTimeout(timer);
  }, [handleFocusInput]);

  return (
    <div className="h-screen bg-[#0a0a0a] text-[#f0f0f0] flex flex-col overflow-hidden">
      <div className="flex-1 overflow-hidden">
        <MainInterface
          messages={messages}
          error={error}
          agentState={agentState}
          config={config}
          onOpenSettings={() => setShowSettings(true)}
        />
      </div>

      <div className="flex-shrink-0">
        <PromptInput
          onSubmit={handlePromptSubmit}
          disabled={!canSendMessage}
        />
      </div>

      <FooterStatusBar
        agentState={agentState}
        config={config}
        onSettingsClick={() => setShowSettings(true)}
      />

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        title="Settings"
        config={config}
        onSave={(newConfig) => {
          updateConfig(newConfig);
          setShowSettings(false);
        }}
      />

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        title="Confirm Plan Execution"
        plan={currentPlan}
        onConfirm={handleConfirmPlan}
        onCancel={handleCancelPlan}
      />
    </div>
  );
};

export default App;
