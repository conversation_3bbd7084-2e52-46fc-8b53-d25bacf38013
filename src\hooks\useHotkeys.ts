import { useEffect, useCallback, useRef } from 'react';

export interface HotkeyConfig {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
  preventDefault?: boolean;
  stopPropagation?: boolean;
  enabled?: boolean;
  description?: string;
}

export interface UseHotkeysOptions {
  enableOnFormTags?: boolean;
  enableOnContentEditable?: boolean;
  filter?: (event: KeyboardEvent) => boolean;
  keyup?: boolean;
  keydown?: boolean;
  splitKey?: string;
}

const DEFAULT_OPTIONS: UseHotkeysOptions = {
  enableOnFormTags: false,
  enableOnContentEditable: false,
  keyup: false,
  keydown: true,
  splitKey: '+',
};

// Key mapping for special keys
const KEY_MAP: Record<string, string> = {
  'cmd': 'meta',
  'command': 'meta',
  'ctrl': 'ctrl',
  'control': 'ctrl',
  'alt': 'alt',
  'option': 'alt',
  'shift': 'shift',
  'enter': 'Enter',
  'return': 'Enter',
  'esc': 'Escape',
  'escape': 'Escape',
  'space': ' ',
  'spacebar': ' ',
  'tab': 'Tab',
  'backspace': 'Backspace',
  'delete': 'Delete',
  'up': 'ArrowUp',
  'down': 'ArrowDown',
  'left': 'ArrowLeft',
  'right': 'ArrowRight',
  'home': 'Home',
  'end': 'End',
  'pageup': 'PageUp',
  'pagedown': 'PageDown',
  'f1': 'F1',
  'f2': 'F2',
  'f3': 'F3',
  'f4': 'F4',
  'f5': 'F5',
  'f6': 'F6',
  'f7': 'F7',
  'f8': 'F8',
  'f9': 'F9',
  'f10': 'F10',
  'f11': 'F11',
  'f12': 'F12',
};

function parseHotkey(hotkey: string, splitKey: string = '+'): HotkeyConfig {
  const keys = hotkey.toLowerCase().split(splitKey).map(k => k.trim());
  
  const config: HotkeyConfig = {
    key: '',
    ctrl: false,
    alt: false,
    shift: false,
    meta: false,
  };

  for (const key of keys) {
    const mappedKey = KEY_MAP[key] || key;
    
    if (mappedKey === 'ctrl') {
      config.ctrl = true;
    } else if (mappedKey === 'alt') {
      config.alt = true;
    } else if (mappedKey === 'shift') {
      config.shift = true;
    } else if (mappedKey === 'meta') {
      config.meta = true;
    } else {
      config.key = mappedKey;
    }
  }

  return config;
}

function matchesHotkey(event: KeyboardEvent, config: HotkeyConfig): boolean {
  // Check modifiers
  if (config.ctrl !== undefined && event.ctrlKey !== config.ctrl) return false;
  if (config.alt !== undefined && event.altKey !== config.alt) return false;
  if (config.shift !== undefined && event.shiftKey !== config.shift) return false;
  if (config.meta !== undefined && event.metaKey !== config.meta) return false;

  // Check key
  if (config.key) {
    const eventKey = event.key;
    const configKey = config.key;
    
    // Case-insensitive comparison for letters
    if (eventKey.toLowerCase() === configKey.toLowerCase()) {
      return true;
    }
    
    // Exact match for special keys
    if (eventKey === configKey) {
      return true;
    }
  }

  return false;
}

function shouldIgnoreEvent(event: KeyboardEvent, options: UseHotkeysOptions): boolean {
  const target = event.target as HTMLElement;
  
  // Check if we should ignore form elements
  if (!options.enableOnFormTags) {
    const tagName = target.tagName.toLowerCase();
    if (['input', 'textarea', 'select'].includes(tagName)) {
      return true;
    }
  }

  // Check if we should ignore contentEditable elements
  if (!options.enableOnContentEditable && target.contentEditable === 'true') {
    return true;
  }

  // Apply custom filter
  if (options.filter && !options.filter(event)) {
    return true;
  }

  return false;
}

export function useHotkeys(
  hotkeys: string | string[],
  callback: (event: KeyboardEvent, hotkey: string) => void,
  options: UseHotkeysOptions = {},
  deps: React.DependencyList = []
): void {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const callbackRef = useRef(callback);
  const optionsRef = useRef(mergedOptions);

  // Update refs when dependencies change
  useEffect(() => {
    callbackRef.current = callback;
    optionsRef.current = mergedOptions;
  });

  const handleKeyEvent = useCallback((event: KeyboardEvent) => {
    const currentOptions = optionsRef.current;
    
    // Check if we should ignore this event
    if (shouldIgnoreEvent(event, currentOptions)) {
      return;
    }

    const hotkeyArray = Array.isArray(hotkeys) ? hotkeys : [hotkeys];
    
    for (const hotkey of hotkeyArray) {
      const config = parseHotkey(hotkey, currentOptions.splitKey);
      
      if (matchesHotkey(event, config)) {
        if (config.preventDefault !== false) {
          event.preventDefault();
        }
        
        if (config.stopPropagation !== false) {
          event.stopPropagation();
        }
        
        callbackRef.current(event, hotkey);
        break;
      }
    }
  }, [hotkeys, ...deps]);

  useEffect(() => {
    const eventType = mergedOptions.keyup ? 'keyup' : 'keydown';
    
    document.addEventListener(eventType, handleKeyEvent);
    
    return () => {
      document.removeEventListener(eventType, handleKeyEvent);
    };
  }, [handleKeyEvent, mergedOptions.keyup]);
}

// Hook for multiple hotkey configurations
export function useHotkeysMap(
  hotkeyMap: Record<string, (event: KeyboardEvent) => void>,
  options: UseHotkeysOptions = {},
  deps: React.DependencyList = []
): void {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const hotkeyMapRef = useRef(hotkeyMap);
  const optionsRef = useRef(mergedOptions);

  useEffect(() => {
    hotkeyMapRef.current = hotkeyMap;
    optionsRef.current = mergedOptions;
  });

  const handleKeyEvent = useCallback((event: KeyboardEvent) => {
    const currentOptions = optionsRef.current;
    const currentHotkeyMap = hotkeyMapRef.current;
    
    if (shouldIgnoreEvent(event, currentOptions)) {
      return;
    }

    for (const [hotkey, callback] of Object.entries(currentHotkeyMap)) {
      const config = parseHotkey(hotkey, currentOptions.splitKey);
      
      if (matchesHotkey(event, config)) {
        if (config.preventDefault !== false) {
          event.preventDefault();
        }
        
        if (config.stopPropagation !== false) {
          event.stopPropagation();
        }
        
        callback(event);
        break;
      }
    }
  }, [deps]);

  useEffect(() => {
    const eventType = mergedOptions.keyup ? 'keyup' : 'keydown';
    
    document.addEventListener(eventType, handleKeyEvent);
    
    return () => {
      document.removeEventListener(eventType, handleKeyEvent);
    };
  }, [handleKeyEvent, mergedOptions.keyup]);
}

// Utility function to get hotkey display string
export function getHotkeyDisplayString(hotkey: string, splitKey: string = '+'): string {
  const config = parseHotkey(hotkey, splitKey);
  const parts: string[] = [];
  
  if (config.meta) parts.push(process.platform === 'darwin' ? '⌘' : 'Ctrl');
  if (config.ctrl && !config.meta) parts.push('Ctrl');
  if (config.alt) parts.push(process.platform === 'darwin' ? '⌥' : 'Alt');
  if (config.shift) parts.push('⇧');
  
  if (config.key) {
    let displayKey = config.key;
    
    // Special display names for certain keys
    const displayMap: Record<string, string> = {
      'Enter': '↵',
      'Escape': 'Esc',
      'ArrowUp': '↑',
      'ArrowDown': '↓',
      'ArrowLeft': '←',
      'ArrowRight': '→',
      ' ': 'Space',
      'Backspace': '⌫',
      'Delete': '⌦',
      'Tab': '⇥',
    };
    
    displayKey = displayMap[config.key] || config.key.toUpperCase();
    parts.push(displayKey);
  }
  
  return parts.join(process.platform === 'darwin' ? '' : '+');
}

export default useHotkeys;
