import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import type { Message, AgentState, AppConfig } from '../types';

// Import highlight.js styles
import 'highlight.js/styles/github-dark.css';

interface MainInterfaceProps {
  messages: Message[];
  error: Error | null;
  agentState: AgentState;
  config: AppConfig;
  onOpenSettings?: () => void;
}

interface MessageComponentProps {
  message: Message;
}

const MessageComponent: React.FC<MessageComponentProps> = ({ message }) => {
  const getMessageStyles = () => {
    switch (message.type) {
      case 'user':
        return 'message-bubble message-bubble-user';
      case 'ai':
        return 'message-bubble message-bubble-ai';
      case 'tool':
        return 'message-bubble message-bubble-tool';
      default:
        return 'message-bubble';
    }
  };

  const getMessageLabel = () => {
    switch (message.type) {
      case 'user':
        return 'You';
      case 'ai':
        return 'AI Assistant';
      case 'tool':
        return 'Tool Output';
      default:
        return 'Message';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={getMessageStyles()}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            message.type === 'user' ? 'bg-[#2563eb]' :
            message.type === 'ai' ? 'bg-[#10b981]' : 'bg-[#f59e0b]'
          }`} />
          <span className="text-sm font-semibold text-[#f0f0f0]">
            {getMessageLabel()}
          </span>
        </div>
        <span className="text-xs text-[#71717a]">
          {formatTimestamp(message.timestamp)}
        </span>
      </div>

      <div className="text-[#f0f0f0]">
        {message.type === 'user' ? (
          // User messages: simple text with preserved whitespace
          <div className="whitespace-pre-wrap font-medium leading-relaxed">
            {message.content}
          </div>
        ) : (
          // AI and tool messages: full markdown rendering
          <div className="prose prose-invert prose-sm max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeHighlight]}
              components={{
                // Custom styling for markdown elements
                h1: ({ children }) => (
                  <h1 className="text-xl font-bold text-[#f0f0f0] mb-4 mt-6 first:mt-0">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-lg font-semibold text-[#f0f0f0] mb-3 mt-5 first:mt-0">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-base font-semibold text-[#f0f0f0] mb-2 mt-4 first:mt-0">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="text-[#f0f0f0] mb-3 last:mb-0 leading-relaxed">
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside text-[#f0f0f0] mb-4 space-y-1 pl-2">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside text-[#f0f0f0] mb-4 space-y-1 pl-2">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-[#f0f0f0] leading-relaxed">{children}</li>
                ),
                blockquote: ({ children }) => (
                  <div className="blockquote">
                    {children}
                  </div>
                ),
                code: ({ children, className, ...props }: React.HTMLAttributes<HTMLElement> & { children?: React.ReactNode }) => {
                  const isInline = !className?.includes('language-');
                  if (isInline) {
                    return (
                      <code className="inline-code">
                        {children}
                      </code>
                    );
                  }
                  return (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  },
                  pre: ({ children }) => (
                    <pre className="code-block">
                      {children}
                    </pre>
                  ),
                  table: ({ children }) => (
                    <div className="table-container">
                      <table>
                        {children}
                      </table>
                    </div>
                  ),
                  thead: ({ children }) => (
                    <thead>
                      {children}
                    </thead>
                  ),
                  th: ({ children }) => (
                    <th>
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td>
                      {children}
                    </td>
                  ),
                  a: ({ children, href }) => (
                    <a
                      href={href}
                      className="text-[#60a5fa] hover:text-[#60a5fa] transition-colors underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {children}
                    </a>
                  ),
                  strong: ({ children }) => (
                    <strong className="font-semibold text-[#f0f0f0]">
                      {children}
                    </strong>
                  ),
                  em: ({ children }) => (
                    <em className="italic text-[#f0f0f0]">
                      {children}
                    </em>
                  ),
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          )}
      </div>
    </div>
  );
};

export const MainInterface: React.FC<MainInterfaceProps> = ({ messages, error, agentState, config, onOpenSettings }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  return (
    <div
      ref={containerRef}
      className="h-full overflow-y-auto custom-scrollbar px-4 py-4 pb-6"
    >
      <div className="w-full min-h-full">
        {/* Error display */}
        {error && (
          <div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-red-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <div className="font-medium text-red-400">Error</div>
                  <div className="text-sm text-red-300">{error?.message || 'An unknown error occurred'}</div>
                </div>
              </div>
              {/* Show settings button if error is about API key */}
              {error?.message?.includes('API key') && onOpenSettings && (
                <button
                  onClick={onOpenSettings}
                  className="px-3 py-1.5 text-xs bg-red-500/20 hover:bg-red-500/30 text-red-300 hover:text-red-200 rounded-md border border-red-500/30 transition-colors"
                >
                  Open Settings
                </button>
              )}
            </div>
          </div>
        )}

        {messages.length === 0 ? (
          <div className="flex items-center justify-center min-h-full">
            <div className="text-center">
              <div className="w-16 h-16 bg-[#1e1e1e] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-md">
                <div className="w-8 h-8 bg-[#2563eb] rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
              </div>
              {!config.llm.apiKey || config.llm.apiKey.trim() === '' ? (
                <>
                  <div className="text-xl font-semibold text-[#f0f0f0] mb-2">Welcome to Arien Agent</div>
                  <div className="text-[#a1a1aa] mb-4">To get started, you need to configure your API key</div>
                  {onOpenSettings && (
                    <button
                      onClick={onOpenSettings}
                      className="px-4 py-2 bg-[#2563eb] hover:bg-[#1d4ed8] text-white rounded-lg transition-colors"
                    >
                      Configure API Key
                    </button>
                  )}
                </>
              ) : (
                <>
                  <div className="text-xl font-semibold text-[#f0f0f0] mb-2">Ready to assist</div>
                  <div className="text-[#a1a1aa]">Start a conversation by typing a message below</div>
                </>
              )}

              {/* Agent status indicator */}
              {agentState.isProcessing && (
                <div className="mt-4 flex items-center justify-center space-x-2 text-[#2563eb]">
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  <span className="text-sm">Processing...</span>
                </div>
              )}
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageComponent key={message.id} message={message} />
            ))}

            {/* Loading indicator for agent processing */}
            {agentState.isProcessing && (
              <div className="mb-4 p-4 bg-[#1e1e1e] border border-[#27272a] rounded-lg">
                <div className="flex items-center space-x-3">
                  <svg className="w-5 h-5 animate-spin text-[#2563eb]" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  <div>
                    <div className="font-medium text-[#f0f0f0]">AI is thinking...</div>
                    <div className="text-sm text-[#a1a1aa]">
                      {agentState.awaitingConfirmation && 'Waiting for confirmation'}
                      {agentState.executingTools && 'Executing tools'}
                      {agentState.streamingResponse && 'Generating response'}
                      {!agentState.awaitingConfirmation && !agentState.executingTools && !agentState.streamingResponse && 'Processing request'}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </div>
    </div>
  );
};
