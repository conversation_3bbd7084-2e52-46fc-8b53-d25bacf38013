import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '../ui';
import type { ConfirmationModalProps, ToolCall } from '../../types';

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  title,
  plan,
  onConfirm,
  onCancel
}) => {
  const [showDetails, setShowDetails] = useState(false);

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const handleCancel = () => {
    onCancel();
    onClose();
  };

  const getRiskLevel = (toolCalls: ToolCall[]): 'low' | 'medium' | 'high' => {
    if (!toolCalls || toolCalls.length === 0) return 'low';

    const highRiskTools = ['delete_file', 'run_shell_command'];
    const mediumRiskTools = ['write_file', 'replace_in_file'];

    const hasHighRisk = toolCalls.some(call => highRiskTools.includes(call.name));
    const hasMediumRisk = toolCalls.some(call => mediumRiskTools.includes(call.name));
    
    if (hasHighRisk) return 'high';
    if (hasMediumRisk) return 'medium';
    return 'low';
  };

  const getRiskColor = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'high': return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'low': return 'text-green-400 bg-green-400/10 border-green-400/20';
    }
  };

  const getToolIcon = (toolName: string) => {
    switch (toolName) {
      case 'run_shell_command':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'write_file':
      case 'read_file':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'delete_file':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        );
      case 'grep':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
    }
  };

  // Don't render if no plan
  if (!plan) {
    return null;
  }

  const riskLevel = getRiskLevel(plan.toolCalls);
  const riskColorClass = getRiskColor(riskLevel);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-6">
        {/* Plan Description */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-text-primary">Plan Overview</h3>
          <p className="text-text-secondary leading-relaxed">{plan.description}</p>
        </div>

        {/* Risk Level Indicator */}
        <div className={`p-4 rounded-lg border ${riskColorClass}`}>
          <div className="flex items-center space-x-2">
            <div className="flex-shrink-0">
              {riskLevel === 'high' && (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              )}
              {riskLevel === 'medium' && (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              )}
              {riskLevel === 'low' && (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div>
              <p className="font-medium capitalize">{riskLevel} Risk Operation</p>
              <p className="text-sm opacity-80">
                {riskLevel === 'high' && 'This operation may modify or delete files/system state'}
                {riskLevel === 'medium' && 'This operation will modify files or system configuration'}
                {riskLevel === 'low' && 'This operation is safe and will only read information'}
              </p>
            </div>
          </div>
        </div>

        {/* Tool Calls Summary */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-text-primary">
              Operations ({plan.toolCalls.length})
            </h3>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-sm text-accent hover:text-accent-hover transition-colors"
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </button>
          </div>

          <div className="space-y-2">
            {plan.toolCalls.map((toolCall, index) => (
              <div key={toolCall.id} className="bg-surface border border-border-primary rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 text-text-tertiary">
                    {getToolIcon(toolCall.name)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-text-primary">{toolCall.name}</span>
                      <span className="text-xs text-text-tertiary">#{index + 1}</span>
                    </div>
                    
                    {showDetails && (
                      <div className="mt-2 space-y-1">
                        <div className="text-sm text-text-secondary">Arguments:</div>
                        <pre className="text-xs bg-primary p-2 rounded border border-border-primary overflow-x-auto">
                          {JSON.stringify(toolCall.arguments, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Reasoning */}
        {showDetails && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-text-primary">Reasoning</h3>
            <div className="bg-surface border border-border-primary rounded-lg p-4">
              <p className="text-text-secondary leading-relaxed whitespace-pre-wrap">
                {plan.reasoning}
              </p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-border-primary">
          <Button variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm}
            variant={riskLevel === 'high' ? 'danger' : 'primary'}
          >
            {riskLevel === 'high' ? 'Execute Anyway' : 'Execute Plan'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmationModal;
